#!/usr/bin/env python3
"""
Analyze the cleaned_dataset.csv to extract meaningful product categories
from the Description column for use in image scraping.
"""

import pandas as pd
import re
from collections import Counter
import numpy as np

def analyze_product_descriptions():
    """Analyze product descriptions to identify categories."""
    
    print("Loading cleaned dataset...")
    df = pd.read_csv('ds_task_1ab/data/dataset/cleaned_dataset.csv')
    print(f"Loaded {len(df)} rows")
    
    # Get unique descriptions
    descriptions = df['Description'].dropna().unique()
    print(f"Found {len(descriptions)} unique product descriptions")
    
    # Extract key product words/categories
    print("\nAnalyzing product descriptions...")
    
    # Common words to extract categories
    category_keywords = []
    
    for desc in descriptions[:100]:  # Sample first 100 for analysis
        if pd.isna(desc):
            continue
        
        # Clean and split description
        words = re.findall(r'\b[A-Za-z]+\b', str(desc).lower())
        category_keywords.extend(words)
    
    # Count word frequency
    word_counts = Counter(category_keywords)
    
    print("\nMost common words in product descriptions:")
    for word, count in word_counts.most_common(50):
        print(f"  {word}: {count}")
    
    # Sample some actual descriptions
    print(f"\nSample product descriptions:")
    sample_descriptions = descriptions[:50]
    for i, desc in enumerate(sample_descriptions, 1):
        if pd.isna(desc):
            continue
        print(f"  {i:2d}. {desc}")
    
    return descriptions

def extract_categories_from_descriptions(descriptions):
    """Extract meaningful categories from product descriptions."""
    
    # Define category patterns based on common retail product types
    category_patterns = {
        # Home & Garden
        'candle': ['candle', 'light', 'holder', 'lantern'],
        'bag': ['bag', 'handbag', 'tote', 'purse'],
        'box': ['box', 'storage', 'container'],
        'mug': ['mug', 'cup', 'coffee', 'tea'],
        'plate': ['plate', 'dish', 'bowl'],
        'towel': ['towel', 'cloth', 'fabric'],
        'cushion': ['cushion', 'pillow', 'soft'],
        'clock': ['clock', 'alarm', 'time'],
        'mirror': ['mirror', 'glass', 'reflection'],
        'frame': ['frame', 'picture', 'photo'],
        
        # Decorative Items
        'ornament': ['ornament', 'decoration', 'decorative'],
        'heart': ['heart', 'love', 'valentine'],
        'star': ['star', 'celestial'],
        'flower': ['flower', 'floral', 'rose', 'daisy'],
        'bird': ['bird', 'robin', 'owl'],
        'butterfly': ['butterfly', 'insect'],
        
        # Kitchen & Dining
        'spoon': ['spoon', 'fork', 'knife', 'cutlery'],
        'jar': ['jar', 'pot', 'container'],
        'bottle': ['bottle', 'flask', 'water'],
        'tray': ['tray', 'serving', 'platter'],
        
        # Toys & Games
        'toy': ['toy', 'play', 'game'],
        'doll': ['doll', 'figure', 'character'],
        'puzzle': ['puzzle', 'jigsaw'],
        'block': ['block', 'building', 'brick'],
        
        # Stationery & Office
        'notebook': ['notebook', 'journal', 'diary'],
        'pen': ['pen', 'pencil', 'marker'],
        'card': ['card', 'greeting', 'postcard'],
        'sticker': ['sticker', 'label'],
        
        # Fashion & Accessories
        'scarf': ['scarf', 'wrap', 'shawl'],
        'hat': ['hat', 'cap', 'beanie'],
        'glove': ['glove', 'mitten'],
        'jewelry': ['necklace', 'bracelet', 'earring', 'ring'],
        
        # Seasonal & Holiday
        'christmas': ['christmas', 'xmas', 'holiday'],
        'easter': ['easter', 'bunny', 'egg'],
        'halloween': ['halloween', 'pumpkin', 'ghost'],
        'birthday': ['birthday', 'party', 'celebration'],
    }
    
    # Count matches for each category
    category_matches = {category: 0 for category in category_patterns}
    
    for desc in descriptions:
        if pd.isna(desc):
            continue
        
        desc_lower = str(desc).lower()
        for category, keywords in category_patterns.items():
            if any(keyword in desc_lower for keyword in keywords):
                category_matches[category] += 1
    
    # Sort by frequency
    sorted_categories = sorted(category_matches.items(), key=lambda x: x[1], reverse=True)
    
    print(f"\nProduct categories found in dataset:")
    print("=" * 50)
    for category, count in sorted_categories:
        if count > 0:
            print(f"  {category}: {count} products")
    
    # Return categories with significant presence
    significant_categories = [cat for cat, count in sorted_categories if count >= 10]
    return significant_categories

if __name__ == "__main__":
    print("=" * 60)
    print("🔍 ANALYZING PRODUCT CATEGORIES FROM DATASET")
    print("=" * 60)
    
    # Analyze descriptions
    descriptions = analyze_product_descriptions()
    
    # Extract categories
    categories = extract_categories_from_descriptions(descriptions)
    
    print(f"\n" + "=" * 60)
    print(f"📊 RECOMMENDED CATEGORIES FOR SCRAPING")
    print("=" * 60)
    print(f"Found {len(categories)} significant categories:")
    for i, category in enumerate(categories, 1):
        print(f"  {i:2d}. {category}")
    
    print(f"\n💡 These categories are based on actual products in your dataset")
    print(f"   and will provide more relevant training data for your CNN model.")
