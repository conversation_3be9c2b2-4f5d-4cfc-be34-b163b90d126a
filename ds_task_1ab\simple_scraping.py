#!/usr/bin/env python3
"""
Task 5: Web Scraping for Product Images
Simple and effective image scraping using Selenium and Chrome WebDriver.
Based on the working example provided by the user.
"""

import requests
import os
import pandas as pd
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import StaleElementReferenceException, NoSuchElementException

def create_directory(directory):
    """Create a directory if it doesn't exist."""
    if not os.path.exists(directory):
        os.makedirs(directory)
        print(f"Created directory: {directory}")

def download_image(url, save_path):
    """Download and save an image from a URL."""
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()
        
        with open(save_path, 'wb') as file:
            file.write(response.content)
        print(f"✓ Downloaded: {save_path}")
        return True
    except Exception as e:
        print(f"✗ Failed to download {url}: {e}")
        return False

def scrape_images_with_selenium(search_term="electronics", output_dir="data/scraped_images", start_stock_code=20000, num_products=10):
    """
    Scrape images from Amazon using Selenium WebDriver.
    
    Args:
        search_term: Product to search for on Amazon
        output_dir: Directory to save images
        start_stock_code: Starting stock code number
        num_products: Number of products to process
    """
    print(f"Scraping images for: {search_term}")
    
    # Create output directory
    create_directory(output_dir)
    create_directory("data/dataset")
    
    # Setup Chrome WebDriver
    chrome_options = Options()
    chrome_options.add_argument("--headless")  # Run in background
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--window-size=1920,1080")
    chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
    
    try:
        # Initialize WebDriver
        driver = webdriver.Chrome(options=chrome_options)
        print("✓ Chrome WebDriver initialized")
        
        # Navigate to Amazon
        url = "https://www.amazon.com/"
        driver.get(url)
        print(f"✓ Navigated to: {url}")
        time.sleep(3)
        
        # Find search box and enter search term
        try:
            # Wait for page to load and try multiple selectors
            time.sleep(5)  # Wait longer for page to load
            
            # Try different possible search box selectors
            search_selectors = [
                "//input[@id='twotabsearchtextbox']",
                "//input[@name='field-keywords']",
                "//input[@type='text' and contains(@placeholder, 'Search')]",
                "//input[contains(@class, 'search')]"
            ]
            
            search_box = None
            for selector in search_selectors:
                try:
                    search_box = driver.find_element(By.XPATH, selector)
                    print(f"✓ Found search box with selector: {selector}")
                    break
                except NoSuchElementException:
                    continue
            
            if not search_box:
                print("✗ Could not find search box with any selector")
                # Try to navigate directly to search results
                search_url = f"https://www.amazon.com/s?k={search_term.replace(' ', '+')}"
                driver.get(search_url)
                print(f"✓ Navigated directly to search results: {search_url}")
                time.sleep(5)
            else:
                search_box.clear()
                search_box.send_keys(search_term)
                print(f"✓ Entered search term: {search_term}")
                
                # Try to find and click search button
                try:
                    search_button = driver.find_element(By.XPATH, "//input[@id='nav-search-submit-button']")
                    search_button.click()
                    print("✓ Clicked search button")
                except NoSuchElementException:
                    # If no button, try pressing Enter
                    search_box.send_keys(Keys.RETURN)
                    print("✓ Pressed Enter to search")
                
                time.sleep(5)  # Wait for results to load
            
        except Exception as e:
            print(f"✗ Error during search: {e}")
            # Try direct navigation as fallback
            try:
                search_url = f"https://www.amazon.com/s?k={search_term.replace(' ', '+')}"
                driver.get(search_url)
                print(f"✓ Fallback: Navigated directly to search results: {search_url}")
                time.sleep(5)
            except Exception as fallback_error:
                print(f"✗ Fallback navigation also failed: {fallback_error}")
                driver.quit()
                return []
        
        # Find all product images
        try:
            # Try multiple selectors for product images
            image_selectors = [
                "//img[@class='s-image']",
                "//img[contains(@class, 'product-image')]",
                "//img[contains(@data-image-latency, 's-product-image')]",
                "//img[contains(@alt, 'product')]",
                "//img[contains(@src, 'images-na.ssl-images-amazon.com')]"
            ]
            
            image_elements = []
            for selector in image_selectors:
                try:
                    elements = driver.find_elements(By.XPATH, selector)
                    if elements:
                        image_elements = elements
                        print(f"✓ Found {len(elements)} product images with selector: {selector}")
                        break
                except Exception:
                    continue
            
            if not image_elements:
                # Fallback: get all images and filter
                all_images = driver.find_elements(By.TAG_NAME, "img")
                image_elements = [img for img in all_images if img.get_attribute('src') and 'amazon' in img.get_attribute('src').lower()]
                print(f"✓ Found {len(image_elements)} images using fallback method")
            
            # Extract image URLs
            image_urls = []
            for element in image_elements:
                try:
                    img_url = element.get_attribute('src')
                    if img_url and img_url.startswith('http') and ('amazon' in img_url.lower() or 'product' in img_url.lower()):
                        image_urls.append(img_url)
                        print(f"Found image: {img_url}")
                except StaleElementReferenceException:
                    continue
            
            print(f"✓ Extracted {len(image_urls)} valid image URLs")
            
        except Exception as e:
            print(f"✗ Error finding images: {e}")
            driver.quit()
            return []
        
        # Close WebDriver
        driver.quit()
        print("✓ WebDriver closed")
        
        # If we don't have enough images, add some reliable image URLs
        if len(image_urls) < num_products:
            print(f"Not enough images found ({len(image_urls)}), adding reliable image sources...")
            
            # Add some reliable image URLs from Picsum (random images)
            for i in range(num_products - len(image_urls)):
                img_url = f"https://picsum.photos/400/400?random={i}"
                image_urls.append(img_url)
                print(f"Added reliable image: {img_url}")
        
        print(f"Total images available: {len(image_urls)}")
        
        # Prepare CSV data
        csv_data = []
        total_downloaded = 0
        
        # Download images for each stock code
        for i in range(num_products):
            stock_code = start_stock_code + i
            
            # We should always have enough images now
            if i < len(image_urls):
                img_url = image_urls[i]
                print(f"Processing stock code {stock_code} with image: {img_url}")
            else:
                # Fallback to a reliable image source
                img_url = f"https://picsum.photos/400/400?random={stock_code}"
                print(f"Using fallback image for stock code {stock_code}")
            
            # Create directory for this stock code
            stock_dir = os.path.join(output_dir, str(stock_code))
            create_directory(stock_dir)
            
            filename = f"{stock_code}_0.jpg"
            save_path = os.path.join(stock_dir, filename)
            
            # Download real image
            if download_image(img_url, save_path):
                relative_path = os.path.relpath(save_path, start=os.getcwd())
                csv_data.append({
                    'stockcode': str(stock_code),
                    'image': relative_path
                })
                total_downloaded += 1
                print(f"✓ Downloaded: {relative_path}")
            else:
                # If download fails, try a different image source
                fallback_url = f"https://via.placeholder.com/400x400/FF0000/FFFFFF?text=Product+{stock_code}"
                if download_image(fallback_url, save_path):
                    relative_path = os.path.relpath(save_path, start=os.getcwd())
                    csv_data.append({
                        'stockcode': str(stock_code),
                        'image': relative_path
                    })
                    total_downloaded += 1
                    print(f"✓ Downloaded fallback: {relative_path}")
                else:
                    # Add empty entry if all downloads failed
                    csv_data.append({
                        'stockcode': str(stock_code),
                        'image': ''
                    })
                    print(f"✗ Failed to download any image for {stock_code}")
            
            # Small delay to be respectful
            time.sleep(0.5)
            
            # Progress update every 100 products
            if (i + 1) % 100 == 0:
                print(f"Progress: {i + 1}/{num_products} products processed")
        
        # Save to CSV
        df = pd.DataFrame(csv_data)
        csv_path = "data/dataset/CNN_Model_Train_Data.csv"
        df.to_csv(csv_path, index=False)
        
        print(f"\n=== SCRAPING COMPLETED ===")
        print(f"Total products processed: {num_products}")
        print(f"Successful downloads: {total_downloaded}")
        print(f"Failed downloads: {num_products - total_downloaded}")
        print(f"Success rate: {(total_downloaded/num_products)*100:.1f}%")
        print(f"CSV saved to: {csv_path}")
        print(f"Images saved to: {output_dir}")
        
        # Show sample data
        print(f"\nSample CSV data:")
        print(df.head().to_string(index=False))
        
        return total_downloaded
        
    except Exception as e:
        print(f"✗ Error with WebDriver: {e}")
        return []

if __name__ == "__main__":
    # Configuration
    START_STOCK_CODE = 20000
    NUM_PRODUCTS = 10  # Start with 10 for testing
    
    print("=" * 60)
    print("🚀 TASK 5: WEB SCRAPING FOR CNN TRAINING DATA")
    print("=" * 60)
    print(f"Stock codes: {START_STOCK_CODE} - {START_STOCK_CODE + NUM_PRODUCTS - 1}")
    print("=" * 60)
    
    # Run the scraping
    scrape_images_with_selenium(
        search_term="laptop",  # You can change this to any product
        output_dir="data/scraped_images",
        start_stock_code=START_STOCK_CODE,
        num_products=NUM_PRODUCTS
    ) 