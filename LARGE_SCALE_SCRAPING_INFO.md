# Large-Scale Image Scraping Configuration

## 🎯 **Updated Configuration**

- **Target Images**: 12,000 images (up from 20)
- **Product Categories**: 80+ categories (up from 8)
- **Stock Code Range**: 20,000 - 31,999

## 📊 **Category Distribution**

With 80+ categories and 12,000 images:
- **~150 images per category** on average
- **Diverse dataset** covering multiple product types

## 🏷️ **Product Categories Added**

### Electronics & Computing (20 categories)
- laptop, desktop computer, smartphone, tablet, smartwatch
- headphones, earbuds, bluetooth speaker, keyboard, mouse
- monitor, webcam, microphone, router, hard drive
- graphics card, motherboard, RAM memory, power supply, CPU processor

### Home & Kitchen (15 categories)
- coffee maker, blender, toaster, microwave, refrigerator
- dishwasher, vacuum cleaner, air fryer, pressure cooker, mixer
- food processor, electric kettle, rice cooker, slow cooker, juicer

### Health & Beauty (12 categories)
- electric toothbrush, hair dryer, electric shaver, massage gun
- fitness tracker, blood pressure monitor, thermometer, humidifier
- air purifier, essential oil diffuser, facial cleansing brush, curling iron

### Sports & Outdoors (15 categories)
- treadmill, exercise bike, dumbbells, yoga mat, resistance bands
- camping tent, sleeping bag, backpack, hiking boots, water bottle
- bicycle, skateboard, golf clubs, tennis racket, basketball

### Tools & Hardware (15 categories)
- drill, screwdriver set, hammer, wrench set, measuring tape
- level, saw, pliers, toolbox, ladder
- power bank, extension cord, flashlight, multimeter, soldering iron

### Fashion & Accessories (10 categories)
- sunglasses, watch, wallet, handbag, backpack
- belt, hat, scarf, gloves, jewelry

### Home Improvement (10 categories)
- light bulb, ceiling fan, door handle, faucet, shower head
- smoke detector, security camera, doorbell, thermostat, switch

### Automotive (10 categories)
- car charger, dash cam, car vacuum, tire pressure gauge, jumper cables
- car phone mount, seat covers, floor mats, air freshener, car wax

## 🚀 **Performance Optimizations**

### Progress Tracking
- **Progress updates every 100 images**
- **Percentage completion tracking**
- **Time estimation and performance metrics**

### Data Safety
- **Automatic backups every 1,000 images**
- **Backup files**: `CNN_Model_Train_Data_backup_1000.csv`, etc.
- **Resume capability** if process is interrupted

### Monitoring
- **Real-time success rate tracking**
- **Category distribution monitoring**
- **Time per image calculation**
- **Total processing time tracking**

## ⚠️ **Important Notes**

1. **Processing Time**: Expect 3-6 hours for 12,000 images
2. **Storage Space**: ~2-4 GB for images + CSV files
3. **Network Usage**: Significant bandwidth required
4. **Rate Limiting**: Built-in delays to respect website limits

## 📁 **Output Structure**

```
data/
├── scraped_images/
│   ├── 20000/
│   │   └── 20000_0.jpg
│   ├── 20001/
│   │   └── 20001_0.jpg
│   └── ... (12,000 folders)
└── dataset/
    ├── CNN_Model_Train_Data.csv
    ├── CNN_Model_Train_Data_backup_1000.csv
    ├── CNN_Model_Train_Data_backup_2000.csv
    └── ... (backup files)
```

## 🎯 **CSV Output Format**

```csv
stockcode,image,category
20000,data/scraped_images/20000/20000_0.jpg,laptop
20001,data/scraped_images/20001/20001_0.jpg,smartphone
20002,data/scraped_images/20002/20002_0.jpg,headphones
...
```

## 🔧 **How to Run**

```bash
python ds_task_1ab/simple_scraping.py
```

The script will display a detailed summary before starting and provide real-time progress updates throughout the scraping process.
